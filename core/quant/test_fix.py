#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的代码
"""

import pandas as pd
import numpy as np

def test_none_empty_fix():
    """测试None.empty修复"""
    print("测试None.empty修复...")
    
    # 测试1: None对象
    df = None
    try:
        # 修复前的代码会出错
        # if df is None or df.empty:
        
        # 修复后的代码
        if df is None:
            print("✅ None检查通过")
        elif df.empty:
            print("DataFrame为空")
        else:
            print("DataFrame有数据")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 测试2: 空DataFrame
    df = pd.DataFrame()
    try:
        if df is None:
            print("DataFrame是None")
        elif df.empty:
            print("✅ 空DataFrame检查通过")
        else:
            print("DataFrame有数据")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 测试3: 有数据的DataFrame
    df = pd.DataFrame({'a': [1, 2, 3]})
    try:
        if df is None:
            print("DataFrame是None")
        elif df.empty:
            print("DataFrame为空")
        else:
            print("✅ 有数据的DataFrame检查通过")
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_batch_processor_logic():
    """测试批量处理器逻辑"""
    print("\n测试批量处理器逻辑...")
    
    # 模拟stock_data字典
    stock_data = {
        '000001': pd.DataFrame({
            'close': [10.0, 10.5, 11.0],
            'high': [10.2, 10.7, 11.2],
            'low': [9.8, 10.3, 10.8],
            'volume': [1000000, 1200000, 1100000]
        }),
        '000002': None,  # 模拟None情况
        '000003': pd.DataFrame()  # 模拟空DataFrame
    }
    
    stock_codes = ['000001', '000002', '000003']
    
    for code in stock_codes:
        try:
            df = stock_data.get(code)
            print(f"处理股票 {code}:")
            
            # 使用修复后的逻辑
            if df is None:
                print(f"  - {code}: 数据为None，跳过")
                continue
            if df.empty:
                print(f"  - {code}: 数据为空，跳过")
                continue
            
            print(f"  - {code}: 数据正常，包含 {len(df)} 行")
            
        except Exception as e:
            print(f"  - {code}: 处理出错: {e}")

if __name__ == "__main__":
    test_none_empty_fix()
    test_batch_processor_logic()
    print("\n✅ 所有测试完成！")
