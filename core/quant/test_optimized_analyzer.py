#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的StockOldAnalyzer
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入，避免触发整个tasks模块的初始化
import importlib.util

# 导入data_models
spec_data = importlib.util.spec_from_file_location("data_models", "tasks/tools/data_models.py")
data_models_module = importlib.util.module_from_spec(spec_data)
spec_data.loader.exec_module(data_models_module)
sys.modules['data_models'] = data_models_module

# 导入stock_old_analyzer
spec = importlib.util.spec_from_file_location("stock_old_analyzer", "tasks/tools/stock_old_analyzer.py")
stock_analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(stock_analyzer_module)
StockOldAnalyzer = stock_analyzer_module.StockOldAnalyzer

def create_test_data(days=120):
    """创建测试股票数据"""
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    # 生成模拟股票数据
    np.random.seed(42)  # 固定随机种子以便复现
    
    # 基础价格走势
    base_price = 10.0
    price_changes = np.random.normal(0.001, 0.02, days)  # 日均涨跌幅
    
    # 累积价格变化
    prices = [base_price]
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))  # 防止价格为负
    
    # 生成OHLC数据
    opens = []
    highs = []
    lows = []
    closes = prices
    volumes = []
    
    for i, close in enumerate(closes):
        # 开盘价基于前一日收盘价
        if i == 0:
            open_price = close
        else:
            gap = np.random.normal(0, 0.005)  # 跳空幅度
            open_price = closes[i-1] * (1 + gap)
        
        # 当日振幅
        daily_range = abs(np.random.normal(0, 0.015))
        high = max(open_price, close) * (1 + daily_range)
        low = min(open_price, close) * (1 - daily_range)
        
        # 成交量（基于价格变化调整）
        price_change = abs(close - open_price) / open_price if open_price > 0 else 0
        base_volume = 1000000
        volume = base_volume * (1 + price_change * 5) * np.random.uniform(0.5, 2.0)
        
        opens.append(open_price)
        highs.append(high)
        lows.append(low)
        volumes.append(int(volume))
    
    # 创建DataFrame
    data = pd.DataFrame({
        'date': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })
    
    return data

def create_strong_trend_data(days=120):
    """创建强势趋势的测试数据"""
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    # 生成上升趋势数据
    base_price = 10.0
    trend_slope = 0.008  # 日均涨幅0.8%
    
    prices = []
    volumes = []
    
    for i in range(days):
        # 基础趋势 + 随机波动
        trend_price = base_price * (1 + trend_slope) ** i
        noise = np.random.normal(0, 0.01)  # 1%的随机波动
        price = trend_price * (1 + noise)
        prices.append(max(price, 0.1))
        
        # 上涨时放量
        if i > 0 and prices[i] > prices[i-1]:
            volume = np.random.uniform(1200000, 2000000)
        else:
            volume = np.random.uniform(800000, 1200000)
        volumes.append(int(volume))
    
    # 生成OHLC
    opens = []
    highs = []
    lows = []
    
    for i, close in enumerate(prices):
        if i == 0:
            open_price = close * 0.99
        else:
            open_price = prices[i-1] * np.random.uniform(0.995, 1.005)
        
        high = max(open_price, close) * np.random.uniform(1.001, 1.02)
        low = min(open_price, close) * np.random.uniform(0.98, 0.999)
        
        opens.append(open_price)
        highs.append(high)
        lows.append(low)
    
    data = pd.DataFrame({
        'date': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': volumes
    })
    
    return data

def test_analyzer_basic():
    """测试分析器基本功能"""
    print("=== 测试基本功能 ===")
    
    # 创建测试数据
    test_data = create_test_data(120)
    
    # 初始化分析器
    analyzer = StockOldAnalyzer(test_data)
    
    # 执行分析
    result = analyzer.analyze_stock('TEST001')
    
    print(f"股票代码: {result.code}")
    print(f"连板数: {result.limit}")
    print(f"10日收益率: {result.ret10:.4f}")
    print(f"20日收益率: {result.ret20:.4f}")
    print(f"MACD信号: {result.macd}")
    print(f"均线信号: {result.ma}")
    print(f"强势趋势: {result.strongTrend}")
    print(f"平台突破: {result.breakoutPlatform}")
    print(f"大分歧: {result.bigDivergence}")
    print(f"资金吸筹: {result.fundAccumulation}")
    print(f"买入信号: {result.buySignal}")
    print()

def test_strong_trend():
    """测试强势趋势识别"""
    print("=== 测试强势趋势识别 ===")
    
    # 创建强势趋势数据
    trend_data = create_strong_trend_data(120)
    
    # 初始化分析器
    analyzer = StockOldAnalyzer(trend_data)
    
    # 执行分析
    result = analyzer.analyze_stock('TREND001')
    
    print(f"股票代码: {result.code}")
    print(f"强势趋势: {result.strongTrend}")
    print(f"均线信号: {result.ma}")
    print(f"买入信号: {result.buySignal}")
    print(f"10日收益率: {result.ret10:.4f}")
    print(f"20日收益率: {result.ret20:.4f}")
    print()

def test_performance_comparison():
    """测试性能对比"""
    print("=== 性能对比测试 ===")
    
    import time
    
    # 创建多个测试数据集
    datasets = []
    for i in range(10):
        np.random.seed(i)
        datasets.append(create_test_data(120))
    
    # 测试分析速度
    start_time = time.time()
    
    results = []
    for i, data in enumerate(datasets):
        analyzer = StockOldAnalyzer(data)
        result = analyzer.analyze_stock(f'PERF{i:03d}')
        results.append(result)
    
    end_time = time.time()
    
    print(f"分析 {len(datasets)} 只股票耗时: {end_time - start_time:.3f} 秒")
    print(f"平均每只股票: {(end_time - start_time) / len(datasets):.3f} 秒")
    
    # 统计买入信号
    buy_signals = sum(1 for r in results if r.buySignal)
    print(f"发出买入信号的股票数: {buy_signals}/{len(results)}")
    print()

def main():
    """主测试函数"""
    print("开始测试优化后的StockOldAnalyzer...")
    print("=" * 50)
    
    try:
        # 基本功能测试
        test_analyzer_basic()
        
        # 强势趋势测试
        test_strong_trend()
        
        # 性能测试
        test_performance_comparison()
        
        print("=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
