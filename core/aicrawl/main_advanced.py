"""
高级爬虫主程序 - 集成完整的反爬虫功能
"""
import asyncio
import logging
import json
import argparse
from typing import List, Dict, Any
from pathlib import Path

from crawler_engine import SmartCrawler, CrawlResult
from config import AntiDetectionConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CrawlerApp:
    """爬虫应用主类"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = SmartCrawler(enable_anti_detection)
        self.results = []
        
    async def crawl_single_stock(self, symbol: str) -> Dict[str, Any]:
        """爬取单个股票"""
        logger.info(f"开始爬取股票: {symbol}")
        
        result = await self.crawler.crawl_stock(symbol)
        
        if result.success and result.data:
            logger.info(f"成功爬取 {symbol}: {result.data.name} - ¥{result.data.current_price}")
            return self._format_result(result)
        else:
            logger.error(f"爬取失败 {symbol}: {result.error}")
            return {"symbol": symbol, "error": result.error, "success": False}
    
    async def crawl_multiple_stocks(self, symbols: List[str], 
                                  concurrent_limit: int = 3) -> List[Dict[str, Any]]:
        """批量爬取股票"""
        logger.info(f"开始批量爬取 {len(symbols)} 个股票")
        
        results = await self.crawler.crawl_multiple_stocks(
            symbols, 
            concurrent_limit=concurrent_limit
        )
        
        formatted_results = []
        for result in results:
            formatted_results.append(self._format_result(result))
            
        # 打印性能报告
        report = self.crawler.get_performance_report()
        logger.info(f"爬取完成 - 性能报告: {report}")
        
        return formatted_results
    
    def _format_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化结果"""
        if result.success and result.data:
            return {
                "success": True,
                "symbol": result.data.symbol,
                "name": result.data.name,
                "current_price": result.data.current_price,
                "change": result.data.change,
                "change_percent": result.data.change_percent,
                "volume": result.data.volume,
                "market_cap": result.data.market_cap,
                "pe_ttm": result.data.pe_ttm,
                "pb": result.data.pb,
                "response_time": result.response_time,
                "url": result.url
            }
        else:
            return {
                "success": False,
                "error": result.error,
                "url": result.url,
                "response_time": result.response_time
            }
    
    def save_results(self, results: List[Dict[str, Any]], filename: str = None):
        """保存结果到文件"""
        if not filename:
            filename = f"crawl_results_{int(asyncio.get_event_loop().time())}.json"
            
        output_path = Path(filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存到: {output_path}")
        
        # 同时保存为CSV格式
        self._save_as_csv(results, output_path.with_suffix('.csv'))
    
    def _save_as_csv(self, results: List[Dict[str, Any]], csv_path: Path):
        """保存为CSV格式"""
        try:
            import pandas as pd
            
            # 过滤成功的结果
            successful_results = [r for r in results if r.get('success')]
            
            if successful_results:
                df = pd.DataFrame(successful_results)
                df.to_csv(csv_path, index=False, encoding='utf-8')
                logger.info(f"CSV结果已保存到: {csv_path}")
        except ImportError:
            logger.warning("pandas未安装，跳过CSV保存")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='雪球股票数据爬虫')
    parser.add_argument('--symbols', nargs='+', default=['SH688627'], 
                       help='股票代码列表')
    parser.add_argument('--concurrent', type=int, default=3,
                       help='并发数量')
    parser.add_argument('--output', type=str, 
                       help='输出文件名')
    parser.add_argument('--disable-anti-detection', action='store_true',
                       help='禁用反检测功能')
    parser.add_argument('--test-mode', action='store_true',
                       help='测试模式，只爬取一个股票')
    
    args = parser.parse_args()
    
    # 创建爬虫应用
    app = CrawlerApp(enable_anti_detection=not args.disable_anti_detection)
    
    try:
        if args.test_mode:
            # 测试模式
            logger.info("运行测试模式")
            result = await app.crawl_single_stock(args.symbols[0])
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            # 批量爬取模式
            results = await app.crawl_multiple_stocks(
                args.symbols, 
                concurrent_limit=args.concurrent
            )
            
            # 打印结果摘要
            successful_count = sum(1 for r in results if r.get('success'))
            logger.info(f"爬取完成: {successful_count}/{len(results)} 成功")
            
            # 保存结果
            app.save_results(results, args.output)
            
            # 打印部分结果
            for result in results[:5]:  # 只打印前5个结果
                if result.get('success'):
                    print(f"{result['symbol']} - {result['name']}: ¥{result['current_price']} ({result['change_percent']:+.2f}%)")
                else:
                    print(f"{result.get('symbol', 'Unknown')}: 爬取失败 - {result.get('error')}")
                    
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")
        raise

# 预定义的股票列表
POPULAR_STOCKS = [
    'SH688627',  # 精智达
    'SZ000001',  # 平安银行
    'SH600036',  # 招商银行
    'SZ000002',  # 万科A
    'SH600519',  # 贵州茅台
    'SZ000858',  # 五粮液
    'SH600276',  # 恒瑞医药
    'SZ002415',  # 海康威视
    'SH600887',  # 伊利股份
    'SZ000725'   # 京东方A
]

async def demo_crawl():
    """演示爬取"""
    print("=== 雪球股票爬虫演示 ===")
    
    app = CrawlerApp(enable_anti_detection=True)
    
    # 爬取热门股票
    demo_symbols = POPULAR_STOCKS[:3]  # 只爬取前3个
    print(f"爬取股票: {demo_symbols}")
    
    results = await app.crawl_multiple_stocks(demo_symbols, concurrent_limit=2)
    
    print("\n=== 爬取结果 ===")
    for result in results:
        if result.get('success'):
            print(f"✓ {result['symbol']} - {result['name']}")
            print(f"  价格: ¥{result['current_price']}")

            # 安全处理可能为None的值
            change = result.get('change')
            change_percent = result.get('change_percent')
            if change is not None and change_percent is not None:
                print(f"  涨跌: {change:+.2f} ({change_percent:+.2f}%)")
            else:
                print(f"  涨跌: N/A")

            market_cap = result.get('market_cap')
            if market_cap:
                print(f"  市值: {market_cap:,.0f}")
            else:
                print(f"  市值: N/A")
        else:
            print(f"✗ {result.get('symbol', 'Unknown')}: {result.get('error')}")
        print()
    
    # 性能报告
    report = app.crawler.get_performance_report()
    print("=== 性能报告 ===")
    for key, value in report.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    # 如果没有命令行参数，运行演示
    import sys
    if len(sys.argv) == 1:
        asyncio.run(demo_crawl())
    else:
        asyncio.run(main())
