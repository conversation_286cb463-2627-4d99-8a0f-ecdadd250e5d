"""
爬虫测试程序 - 测试反爬虫功能
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_engine import SmartCrawler
from config import AntiDetectionConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_crawl():
    """测试基础爬取功能"""
    print("=== 测试基础爬取功能 ===")
    
    crawler = SmartCrawler(enable_anti_detection=True)
    
    # 测试目标股票
    test_symbol = "SH688627"  # 精智达
    
    try:
        result = await crawler.crawl_stock(test_symbol)
        
        if result.success:
            print(f"✓ 爬取成功!")
            print(f"股票代码: {result.data.symbol}")
            print(f"股票名称: {result.data.name}")
            print(f"当前价格: ¥{result.data.current_price}")
            if result.data.change_percent:
                print(f"涨跌幅: {result.data.change_percent:+.2f}%")
            print(f"响应时间: {result.response_time:.2f}s")
            
            # 显示原始数据（部分）
            if result.data.raw_data:
                print(f"原始数据字段数: {len(result.data.raw_data)}")
                
        else:
            print(f"✗ 爬取失败: {result.error}")
            
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        
    # 性能报告
    report = crawler.get_performance_report()
    print(f"\n性能报告: {report}")

async def test_multiple_crawl():
    """测试批量爬取"""
    print("\n=== 测试批量爬取功能 ===")
    
    crawler = SmartCrawler(enable_anti_detection=True)
    
    # 测试多个股票
    test_symbols = ["SH688627", "SZ000001", "SH600036"]
    
    try:
        results = await crawler.crawl_multiple_stocks(test_symbols, concurrent_limit=2)
        
        print(f"批量爬取完成，共 {len(results)} 个结果:")
        
        for result in results:
            if result.success:
                print(f"✓ {result.data.symbol} - {result.data.name}: ¥{result.data.current_price}")
            else:
                print(f"✗ 爬取失败: {result.error}")
                
    except Exception as e:
        print(f"✗ 批量测试异常: {e}")
        
    # 最终性能报告
    report = crawler.get_performance_report()
    print(f"\n最终性能报告: {report}")

async def test_anti_detection_features():
    """测试反检测功能"""
    print("\n=== 测试反检测功能 ===")
    
    # 测试User-Agent轮换
    print("测试User-Agent轮换:")
    for i in range(3):
        ua = AntiDetectionConfig.get_random_user_agent()
        print(f"  {i+1}. {ua[:50]}...")
    
    # 测试请求头轮换
    print("\n测试请求头轮换:")
    for i in range(2):
        headers = AntiDetectionConfig.get_random_headers()
        print(f"  {i+1}. Accept: {headers.get('Accept', 'N/A')[:50]}...")
    
    # 测试延迟控制
    print("\n测试延迟控制:")
    for i in range(3):
        delay = AntiDetectionConfig.get_random_delay()
        print(f"  {i+1}. 随机延迟: {delay:.2f}s")

async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    crawler = SmartCrawler(enable_anti_detection=True)
    
    # 测试无效URL
    try:
        result = await crawler.crawl_stock("INVALID_SYMBOL")
        if not result.success:
            print(f"✓ 正确处理无效股票代码: {result.error}")
        else:
            print("✗ 应该失败但却成功了")
    except Exception as e:
        print(f"✓ 正确捕获异常: {e}")

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    print(f"User-Agent池大小: {len(AntiDetectionConfig.USER_AGENTS)}")
    print(f"请求头模板数量: {len(AntiDetectionConfig.HEADERS_TEMPLATES)}")
    print(f"代理池大小: {len(AntiDetectionConfig.PROXY_POOLS)}")
    print(f"最小延迟: {AntiDetectionConfig.REQUEST_DELAYS['min_delay']}s")
    print(f"最大延迟: {AntiDetectionConfig.REQUEST_DELAYS['max_delay']}s")
    
    # 测试雪球配置
    xueqiu_config = AntiDetectionConfig.XUEQIU_CONFIG
    print(f"雪球基础URL: {xueqiu_config['base_url']}")
    print(f"股票URL模式: {xueqiu_config['stock_url_pattern']}")

async def run_all_tests():
    """运行所有测试"""
    print("开始运行爬虫测试套件...")
    print("=" * 50)
    
    # 配置测试
    test_config()
    
    # 反检测功能测试
    await test_anti_detection_features()
    
    # 基础爬取测试
    await test_basic_crawl()
    
    # 批量爬取测试
    await test_multiple_crawl()
    
    # 错误处理测试
    await test_error_handling()
    
    print("\n" + "=" * 50)
    print("测试套件运行完成!")

if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "basic":
            asyncio.run(test_basic_crawl())
        elif sys.argv[1] == "multiple":
            asyncio.run(test_multiple_crawl())
        elif sys.argv[1] == "anti-detection":
            asyncio.run(test_anti_detection_features())
        elif sys.argv[1] == "error":
            asyncio.run(test_error_handling())
        elif sys.argv[1] == "config":
            test_config()
        else:
            print("可用的测试选项:")
            print("  basic - 基础爬取测试")
            print("  multiple - 批量爬取测试") 
            print("  anti-detection - 反检测功能测试")
            print("  error - 错误处理测试")
            print("  config - 配置测试")
    else:
        # 运行所有测试
        asyncio.run(run_all_tests())
