# Blackbear AI 反爬虫爬虫系统

专为雪球网站设计的高级反爬虫爬虫系统，集成了完整的反检测功能和智能数据提取策略。

## 🚀 核心特性

### 反爬虫绕过功能
- **智能User-Agent轮换**: 模拟真实浏览器访问
- **动态请求头管理**: 随机化请求头，避免指纹识别
- **频率控制系统**: 智能延迟控制，模拟人类访问模式
- **代理池管理**: 支持代理轮换和失败检测
- **浏览器指纹伪装**: 完整的浏览器环境模拟
- **自适应重试机制**: 智能错误处理和重试策略

### 数据提取功能
- **多源数据提取**: 支持JavaScript变量和DOM元素提取
- **智能数据解析**: 自动识别和清理股票数据
- **结构化输出**: 标准化的股票数据格式
- **数据验证**: 自动验证数据完整性和合理性

### 监控和优化
- **实时性能监控**: 成功率、响应时间统计
- **自适应调整**: 根据成功率动态调整策略
- **详细日志记录**: 完整的爬取过程记录

## 📁 项目结构

```
core/aicrawl/
├── config.py              # 配置文件（User-Agent池、代理配置等）
├── anti_detection.py      # 反检测核心模块
├── extractors.py          # 数据提取器
├── crawler_engine.py      # 爬虫引擎
├── main_advanced.py       # 高级主程序
├── test_crawler.py        # 测试程序
├── main.py               # 原始简单程序
└── requirements.txt      # 依赖包
```

## 🛠 环境搭建

1. **安装Python依赖**
```bash
cd /Users/<USER>/Repo/Blackbear/core/aicrawl
pip install -r requirements.txt
```

2. **安装浏览器依赖**
```bash
python -m playwright install --with-deps chromium
```

3. **验证安装**
```bash
crawl4ai-doctor
```

## 🎯 使用方法

### 快速开始 - 演示模式
```bash
python main_advanced.py
```
这将运行演示模式，爬取几个热门股票。

### 单个股票爬取
```bash
python main_advanced.py --symbols SH688627 --test-mode
```

### 批量股票爬取
```bash
python main_advanced.py --symbols SH688627 SZ000001 SH600036 --concurrent 3
```

### 高级参数
```bash
python main_advanced.py \
  --symbols SH688627 SZ000001 \
  --concurrent 2 \
  --output my_results.json \
  --disable-anti-detection
```

### 测试功能
```bash
# 运行所有测试
python test_crawler.py

# 运行特定测试
python test_crawler.py basic          # 基础爬取测试
python test_crawler.py multiple       # 批量爬取测试
python test_crawler.py anti-detection # 反检测功能测试
python test_crawler.py config         # 配置测试
```

## ⚙️ 配置说明

### 代理配置
编辑 `config.py` 中的 `PROXY_POOLS`：
```python
PROXY_POOLS = [
    {"http": "http://proxy1:port", "https": "https://proxy1:port"},
    {"http": "http://proxy2:port", "https": "https://proxy2:port"},
]
```

### 频率控制
调整 `REQUEST_DELAYS` 参数：
```python
REQUEST_DELAYS = {
    "min_delay": 2,      # 最小延迟(秒)
    "max_delay": 8,      # 最大延迟(秒)
    "burst_delay": 15,   # 突发请求后的延迟(秒)
    "error_delay": 30    # 错误后的延迟(秒)
}
```

## 📊 输出格式

### JSON格式
```json
{
  "success": true,
  "symbol": "SH688627",
  "name": "精智达",
  "current_price": 93.60,
  "change": 3.61,
  "change_percent": 4.01,
  "volume": 4142600,
  "market_cap": 8799500174,
  "pe_ttm": 112.26,
  "pb": 5.24,
  "response_time": 2.34,
  "url": "https://xueqiu.com/S/SH688627"
}
```

### CSV格式
自动生成CSV文件，包含所有成功爬取的股票数据。

## 🔧 API使用

### 编程接口
```python
from crawler_engine import SmartCrawler

# 创建爬虫实例
crawler = SmartCrawler(enable_anti_detection=True)

# 爬取单个股票
result = await crawler.crawl_stock("SH688627")

# 批量爬取
results = await crawler.crawl_multiple_stocks(
    ["SH688627", "SZ000001"],
    concurrent_limit=3
)

# 获取性能报告
report = crawler.get_performance_report()
```

## 🛡️ 反爬虫策略详解

### 1. 浏览器指纹伪装
- 随机User-Agent轮换
- 真实浏览器参数模拟
- 禁用自动化检测标识

### 2. 请求模式优化
- 智能频率控制
- 随机延迟注入
- 人类行为模拟

### 3. 网络层保护
- 代理池轮换
- IP失败检测
- 自动重试机制

### 4. 数据提取策略
- JavaScript变量提取
- DOM元素备用提取
- 多重数据验证

## 📈 性能优化

### 并发控制
- 默认并发数：3
- 可根据网络情况调整
- 自动负载均衡

### 内存管理
- 流式数据处理
- 及时释放资源
- 批量结果保存

### 错误恢复
- 智能重试机制
- 代理自动切换
- 降级策略支持

## ⚠️ 注意事项

1. **合规使用**
   - 遵守网站robots.txt规则
   - 控制爬取频率，避免对服务器造成压力
   - 仅用于学习和研究目的

2. **代理配置**
   - 建议使用高质量代理
   - 定期更新代理池
   - 监控代理可用性

3. **数据准确性**
   - 股票数据仅供参考
   - 建议与官方数据对比验证
   - 注意市场开闭盘时间

4. **系统资源**
   - 监控CPU和内存使用
   - 适当调整并发数
   - 定期清理日志文件

## 🔍 故障排除

### 常见问题

1. **爬取失败率高**
   - 检查网络连接
   - 更新代理池
   - 降低并发数

2. **数据提取不完整**
   - 检查网站结构变化
   - 更新选择器配置
   - 启用详细日志

3. **性能问题**
   - 调整延迟参数
   - 优化并发设置
   - 检查系统资源

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from test_crawler import test_basic_crawl
import asyncio
asyncio.run(test_basic_crawl())
"
```

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 完整的反爬虫架构
- ✅ 智能数据提取策略
- ✅ 性能监控和自适应调整
- ✅ 批量爬取支持
- ✅ 详细的错误处理

### v1.0.0 (原始版本)
- ✅ 基础crawl4ai集成
- ✅ 简单数据提取

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。