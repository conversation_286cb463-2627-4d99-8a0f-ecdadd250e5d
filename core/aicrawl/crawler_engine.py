"""
爬虫引擎 - 集成反检测和数据提取的核心爬虫引擎
"""
import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.cache_context import CacheMode

from anti_detection import AntiDetectionManager, RequestResult
from extractors import MultiSourceExtractor, StockData
from config import AntiDetectionConfig

logger = logging.getLogger(__name__)

@dataclass
class CrawlResult:
    """爬取结果"""
    success: bool
    data: Optional[StockData] = None
    raw_html: Optional[str] = None
    error: Optional[str] = None
    url: Optional[str] = None
    response_time: Optional[float] = None
    attempts: int = 1

class XueqiuCrawler:
    """雪球网站专用爬虫"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.anti_detection = AntiDetectionManager() if enable_anti_detection else None
        self.extractor = MultiSourceExtractor()
        self.session_cookies = {}
        
    async def crawl_stock(self, symbol: str, **kwargs) -> CrawlResult:
        """爬取单个股票数据"""
        url = AntiDetectionConfig.XUEQIU_CONFIG["stock_url_pattern"].format(symbol=symbol)
        return await self.crawl_url(url, **kwargs)
    
    async def crawl_url(self, url: str, **kwargs) -> CrawlResult:
        """爬取指定URL"""
        start_time = time.time()
        
        try:
            if self.anti_detection:
                # 使用反检测管理器
                result = await self.anti_detection.execute_request(
                    self._crawl_with_crawl4ai, url, **kwargs
                )
            else:
                # 直接爬取
                result = await self._crawl_with_crawl4ai(url, **kwargs)
                
            response_time = time.time() - start_time
            
            if result.success and result.data:
                # 提取数据
                stock_data = self.extractor.extract(result.data, url, 'xueqiu')
                return CrawlResult(
                    success=True,
                    data=stock_data,
                    raw_html=result.data,
                    url=url,
                    response_time=response_time
                )
            else:
                return CrawlResult(
                    success=False,
                    error=result.error,
                    url=url,
                    response_time=response_time
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"爬取失败 {url}: {e}")
            return CrawlResult(
                success=False,
                error=str(e),
                url=url,
                response_time=response_time
            )
    
    async def _crawl_with_crawl4ai(self, url: str, **kwargs) -> RequestResult:
        """使用crawl4ai进行爬取"""
        try:
            # 准备请求参数
            if self.anti_detection:
                request_params = await self.anti_detection.prepare_request(url)
                browser_config = request_params["browser_config"]
                headers = request_params["headers"]
                proxy = request_params["proxy"]
            else:
                browser_config = {"headless": True}
                headers = {}
                proxy = None
            
            # 创建爬虫配置
            config = CrawlerRunConfig(
                cache_mode=CacheMode.DISABLED,
                **kwargs
            )
            
            # 初始化爬虫
            crawler_kwargs = {
                "browser_type": "chromium",
                "verbose": False,
                **browser_config
            }

            # 如果有代理，添加代理配置
            if proxy:
                crawler_kwargs["proxy"] = proxy.get("http", "").replace("http://", "")

            async with AsyncWebCrawler(**crawler_kwargs) as crawler:
                # 执行爬取，将headers传递给arun方法
                result = await crawler.arun(
                    url=url,
                    config=config,
                    headers=headers,
                    timeout=30000
                )
                
                if result and result.success:
                    # 优先使用HTML，如果没有则使用markdown
                    content = result.html if hasattr(result, 'html') and result.html else result.markdown
                    return RequestResult(
                        success=True,
                        data=content,
                        status_code=200
                    )
                else:
                    error_msg = getattr(result, 'error', '爬取失败')
                    return RequestResult(
                        success=False,
                        error=error_msg
                    )
                    
        except Exception as e:
            logger.error(f"crawl4ai爬取异常: {e}")
            return RequestResult(
                success=False,
                error=str(e)
            )
    
    async def crawl_multiple_stocks(self, symbols: List[str], 
                                  concurrent_limit: int = 3,
                                  **kwargs) -> List[CrawlResult]:
        """批量爬取多个股票"""
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def crawl_with_semaphore(symbol):
            async with semaphore:
                return await self.crawl_stock(symbol, **kwargs)
        
        tasks = [crawl_with_semaphore(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(CrawlResult(
                    success=False,
                    error=str(result),
                    url=f"https://xueqiu.com/S/{symbols[i]}"
                ))
            else:
                processed_results.append(result)
                
        return processed_results
    
    def update_session_cookies(self, cookies: Dict[str, str]):
        """更新会话Cookie"""
        self.session_cookies.update(cookies)

class CrawlerMonitor:
    """爬虫监控器"""
    
    def __init__(self):
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_response_time": 0,
            "errors": []
        }
        
    def record_result(self, result: CrawlResult):
        """记录爬取结果"""
        self.stats["total_requests"] += 1
        
        if result.success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
            if result.error:
                self.stats["errors"].append({
                    "url": result.url,
                    "error": result.error,
                    "timestamp": time.time()
                })
        
        if result.response_time:
            self.stats["total_response_time"] += result.response_time
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.stats["total_requests"] == 0:
            return 0.0
        return self.stats["successful_requests"] / self.stats["total_requests"]
    
    def get_average_response_time(self) -> float:
        """获取平均响应时间"""
        if self.stats["successful_requests"] == 0:
            return 0.0
        return self.stats["total_response_time"] / self.stats["successful_requests"]
    
    def get_stats_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{self.get_success_rate():.2%}",
            "average_response_time": f"{self.get_average_response_time():.2f}s",
            "recent_errors": self.stats["errors"][-5:]  # 最近5个错误
        }
    
    def reset_stats(self):
        """重置统计数据"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_response_time": 0,
            "errors": []
        }

class SmartCrawler:
    """智能爬虫 - 集成监控和自适应功能"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = XueqiuCrawler(enable_anti_detection)
        self.monitor = CrawlerMonitor()
        self.adaptive_delay = 2.0  # 自适应延迟
        
    async def crawl_stock(self, symbol: str, **kwargs) -> CrawlResult:
        """智能爬取股票数据"""
        result = await self.crawler.crawl_stock(symbol, **kwargs)
        self.monitor.record_result(result)
        
        # 自适应调整
        await self._adaptive_adjustment(result)
        
        return result
    
    async def crawl_multiple_stocks(self, symbols: List[str], **kwargs) -> List[CrawlResult]:
        """智能批量爬取"""
        results = await self.crawler.crawl_multiple_stocks(symbols, **kwargs)
        
        for result in results:
            self.monitor.record_result(result)
            
        # 批量自适应调整
        await self._batch_adaptive_adjustment(results)
        
        return results
    
    async def _adaptive_adjustment(self, result: CrawlResult):
        """自适应调整策略"""
        if not result.success:
            # 失败时增加延迟
            self.adaptive_delay = min(self.adaptive_delay * 1.5, 30.0)
            await asyncio.sleep(self.adaptive_delay)
        else:
            # 成功时逐渐减少延迟
            self.adaptive_delay = max(self.adaptive_delay * 0.9, 1.0)
    
    async def _batch_adaptive_adjustment(self, results: List[CrawlResult]):
        """批量自适应调整"""
        success_rate = sum(1 for r in results if r.success) / len(results)
        
        if success_rate < 0.5:
            # 成功率低时大幅增加延迟
            self.adaptive_delay = min(self.adaptive_delay * 2.0, 60.0)
            logger.warning(f"成功率低 ({success_rate:.2%})，增加延迟到 {self.adaptive_delay}s")
        elif success_rate > 0.8:
            # 成功率高时减少延迟
            self.adaptive_delay = max(self.adaptive_delay * 0.8, 1.0)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        stats = self.monitor.get_stats_summary()
        stats["adaptive_delay"] = f"{self.adaptive_delay:.2f}s"
        return stats
